# PLR Vault Admin Panel

A comprehensive web-based admin panel for managing PLR (Private Label Rights) content, users, and administrators.

## 🌐 Live URL

**Admin Panel:** https://myapp-59f81.web.app

## 🔐 Access Levels

### Super Admin

- **Email:** <EMAIL>
- **Permissions:**
  - Manage all content (add, edit, delete, view)
  - Manage all users (view, promote to admin, delete)
  - Manage all admins (add, remove admin privileges)
  - View dashboard statistics
  - Full system access

### Admin

- **Permissions:**
  - Manage content (add, edit, delete, view)
  - View dashboard statistics
  - Cannot manage users or other admins

### User Access

- Only users with `admin` or `super_admin` role in the Firebase Realtime Database can access the admin panel
- Regular users will be denied access

## 📱 Features

### Dashboard

- Total content count
- Total users count
- Total admins count
- Total categories (20 predefined categories)

### Content Management

- **Add Content:** Upload files with metadata (title, description, category)
- **View Content:** Category-wise listing with file information
- **Filter Content:** Filter by category
- **Delete Content:** Remove content from both storage and database
- **File Support:** All file types (PDF, DOC, MP4, images, archives, etc.)

### User Management (Super Admin Only)

- View all registered users
- Promote users to admin
- Track user registration and login activity

### Admin Management (Super Admin Only)

- View all admins
- Add new admins (from existing users)
- Remove admin privileges
- Cannot remove super admin privileges

## 🏗️ Technical Architecture

### Frontend

- **Framework:** Vanilla JavaScript with Bootstrap 5
- **Styling:** Custom CSS with modern design
- **Icons:** Font Awesome 6
- **Responsive:** Mobile-friendly design

### Backend

- **Authentication:** Firebase Authentication
- **Database:** Firebase Realtime Database
- **Storage:** Firebase Cloud Storage
- **Hosting:** Firebase Hosting

### Database Structure

```
users/
  {uid}/
    email: string
    displayName: string
    role: "user" | "admin" | "super_admin"
    signUpMethod: "email" | "google"
    createdAt: ISO string
    lastLoginAt: ISO string
    isActive: boolean

content/
  {contentId}/
    title: string
    description: string
    category: string
    fileName: string
    originalFileName: string
    fileType: string
    fileSize: number
    downloadURL: string
    createdAt: ISO string
    createdBy: string
```

### Storage Structure

```
content/
  {category}/
    {timestamp}_{filename}
```

## 🚀 Deployment

The admin panel is automatically deployed to Firebase Hosting at:
**https://myapp-59f81.web.app**

### Manual Deployment

```bash
cd PLR_Vault/admin-webapp
firebase deploy --only hosting
```

## 📋 Categories

The system supports 20 predefined categories:

1. Business & Entrepreneurship
2. Health & Wellness
3. Technology & Innovation
4. Recipes & Nutrition
5. MRR Video Courses
6. Finance & Investment
7. Self-Improvement & Motivation
8. Marketing & Branding
9. Design & Templates
10. Spirituality & Mindfulness
11. Career & Freelancing
12. AI & Automation
13. Education & eLearning
14. Legal & Business Docs
15. eCommerce & Dropshipping
16. Parenting & Family
17. Fashion & Beauty
18. Travel & Lifestyle
19. Kids & Learning
20. Entertainment & Fun

## 🔧 Setup Instructions

### Prerequisites

- Firebase CLI installed
- Access to Firebase project: myapp-59f81

### Local Development

1. Clone the repository
2. Navigate to `PLR_Vault/admin-webapp`
3. Serve locally: `firebase serve`
4. Access at: http://localhost:5000

### Adding New Admins

1. User must first register in the mobile app
2. Super admin logs into the admin panel
3. Go to "Manage Admins" section
4. Click "Add Admin"
5. Enter the user's email address
6. User will now have admin access

## 🔒 Security Features

- **Role-based access control**
- **Firebase Authentication integration**
- **Super admin email hardcoded for security**
- **Database rules enforce permissions**
- **Secure file upload with progress tracking**

## 📱 Mobile App Integration

The admin panel is fully integrated with the PLR Vault mobile app:

- Users registered in the app appear in the admin panel
- Content added via admin panel is available in the app
- User roles are synchronized between app and admin panel
- Download tracking and unlock status managed centrally

## 🎯 Usage Workflow

1. **Super Admin Setup:**

   - Super admin (<EMAIL>) logs in
   - Automatically granted super admin privileges

2. **Adding Content:**

   - Admin/Super admin logs in
   - Goes to "Manage Content"
   - Clicks "Add Content"
   - Fills form and uploads file
   - Content becomes available in mobile app

3. **Managing Users:**

   - Super admin views all registered users
   - Can promote users to admin
   - Can delete inactive users

4. **Managing Admins:**
   - Super admin can add/remove admin privileges
   - Admins can only manage content

## 🌟 Key Benefits

- **Centralized Management:** Single dashboard for all admin tasks
- **User-Friendly Interface:** Modern, responsive design
- **Real-time Updates:** Changes reflect immediately
- **Secure Access:** Role-based permissions
- **File Management:** Organized storage by category
- **Progress Tracking:** Upload progress indicators
- **Mobile Integration:** Seamless sync with mobile app
