// Admin Panel JavaScript - VERSION 2.1
console.log("🚀 Loading Admin Panel v2.1 - " + new Date().toISOString());

import { auth, database, storage } from "./firebase-config.js";
import {
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import {
  ref,
  get,
  set,
  push,
  remove,
  update,
  onValue,
  off,
  child,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-database.js";
import {
  ref as storageRef,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  uploadBytes,
  getBlob,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";

// Global variables
let currentUser = null;
let currentUserRole = null;

// Categories list - Full names as they should appear in the app
const CATEGORIES = [
  "Business & Entrepreneurship",
  "Health & Wellness",
  "Technology & Innovation",
  "Recipes & Nutrition",
  "MRR Video Courses",
  "Finance & Investment",
  "Self-Improvement & Motivation",
  "Marketing & Branding",
  "Design & Templates",
  "Spirituality & Mindfulness",
  "Career & Freelancing",
  "AI & Automation",
  "Education & eLearning",
  "Legal & Business Docs",
  "eCommerce & Dropshipping",
  "Parenting & Family",
  "Fashion & Beauty",
  "Travel & Lifestyle",
  "Kids & Learning",
  "Entertainment & Fun",
];

// Mapping between database/storage folder names and full category names
const CATEGORY_MAPPING = {
  // Database/Storage name -> Full category name
  Business: "Business & Entrepreneurship",
  Health: "Health & Wellness",
  Technology: "Technology & Innovation",
  Recipes: "Recipes & Nutrition",
  MRR: "MRR Video Courses",
  Finance: "Finance & Investment",
  "Self-Improvement": "Self-Improvement & Motivation",
  Marketing: "Marketing & Branding",
  Design: "Design & Templates",
  Spirituality: "Spirituality & Mindfulness",
  Career: "Career & Freelancing",
  AI: "AI & Automation",
  Education: "Education & eLearning",
  Legal: "Legal & Business Docs",
  eCommerce: "eCommerce & Dropshipping",
  Parenting: "Parenting & Family",
  Fashion: "Fashion & Beauty",
  Travel: "Travel & Lifestyle",
  Kids: "Kids & Learning",
  Entertainment: "Entertainment & Fun",
};

// Reverse mapping - Full category name -> Database/Storage name
const REVERSE_CATEGORY_MAPPING = {};
Object.entries(CATEGORY_MAPPING).forEach(([short, full]) => {
  REVERSE_CATEGORY_MAPPING[full] = short;
});

// Smart category detection function
function getCorrectCategory(item) {
  const title = (item.title || "").toLowerCase();
  const description = (item.description || "").toLowerCase();
  const fileName = (item.fileName || item.originalFileName || "").toLowerCase();
  const fileType = (item.fileType || "").toLowerCase();

  // 1. ALL VIDEO CONTENT goes to MRR Video Courses (HIGHEST PRIORITY)
  const videoTypes = [
    "video/",
    "mp4",
    "avi",
    "mov",
    "wmv",
    "flv",
    "webm",
    "mkv",
    "m4v",
  ];
  const isVideo = videoTypes.some(
    (type) =>
      fileType.includes(type) ||
      fileName.includes(".mp4") ||
      fileName.includes(".avi") ||
      fileName.includes(".mov") ||
      fileName.includes(".wmv") ||
      fileName.includes(".flv") ||
      fileName.includes(".webm") ||
      fileName.includes(".mkv") ||
      fileName.includes(".m4v")
  );

  if (isVideo) {
    console.log(
      `📹 Video content detected: ${item.title} -> MRR Video Courses`
    );
    return "MRR";
  }

  // 2. ALL RECIPE CONTENT goes to Recipes & Nutrition (check title and description)
  const recipeKeywords = [
    "recipe",
    "cooking",
    "food",
    "nutrition",
    "meal",
    "diet",
    "kitchen",
    "ingredient",
    "cook",
  ];
  const isRecipe = recipeKeywords.some(
    (keyword) =>
      title.includes(keyword) ||
      description.includes(keyword) ||
      fileName.includes(keyword)
  );

  if (isRecipe) {
    console.log(
      `🍳 Recipe content detected: ${item.title} -> Recipes & Nutrition`
    );
    return "Recipes";
  }

  // 3. Map existing short categories to full names
  const currentCategory = item.category || "";

  // Direct mapping from CATEGORY_MAPPING
  if (CATEGORY_MAPPING[currentCategory]) {
    console.log(
      `🔄 Mapped category: ${currentCategory} -> ${CATEGORY_MAPPING[currentCategory]}`
    );
    return CATEGORY_MAPPING[currentCategory];
  }

  // EXACT MATCHES for database exceptions (handle stored short names)
  const categoryTrimmed = currentCategory.trim();

  if (categoryTrimmed === "Business") {
    console.log(
      `💼 Database exception - Business: ${item.title} -> Business & Entrepreneurship`
    );
    return "Business & Entrepreneurship";
  }

  if (categoryTrimmed === "Health") {
    console.log(
      `🏥 Database exception - Health: ${item.title} -> Health & Wellness`
    );
    return "Health & Wellness";
  }

  if (categoryTrimmed === "Technology") {
    console.log(
      `💻 Database exception - Technology: ${item.title} -> Technology & Innovation`
    );
    return "Technology & Innovation";
  }

  if (categoryTrimmed === "Finance") {
    console.log(
      `💰 Database exception - Finance: ${item.title} -> Finance & Investment`
    );
    return "Finance & Investment";
  }

  if (categoryTrimmed === "Marketing") {
    console.log(
      `📢 Database exception - Marketing: ${item.title} -> Marketing & Branding`
    );
    return "Marketing & Branding";
  }

  if (categoryTrimmed === "Education") {
    console.log(
      `🎓 Database exception - Education: ${item.title} -> Education & eLearning`
    );
    return "Education & eLearning";
  }

  if (categoryTrimmed === "Self-Improvement") {
    console.log(
      `🌟 Database exception - Self-Improvement: ${item.title} -> Self-Improvement & Motivation`
    );
    return "Self-Improvement & Motivation";
  }

  if (categoryTrimmed === "Design") {
    console.log(
      `🎨 Database exception - Design: ${item.title} -> Design & Templates`
    );
    return "Design & Templates";
  }

  // Handle common variations
  const categoryLower = currentCategory.toLowerCase();

  if (
    categoryLower.includes("business") ||
    categoryLower.includes("entrepreneur")
  ) {
    console.log(
      `💼 Business content detected: ${item.title} -> Business & Entrepreneurship`
    );
    return "Business & Entrepreneurship";
  }

  if (
    categoryLower.includes("health") ||
    categoryLower.includes("wellness") ||
    categoryLower.includes("medical")
  ) {
    console.log(
      `🏥 Health content detected: ${item.title} -> Health & Wellness`
    );
    return "Health & Wellness";
  }

  if (
    categoryLower.includes("technology") ||
    categoryLower.includes("tech") ||
    categoryLower.includes("innovation")
  ) {
    console.log(
      `💻 Technology content detected: ${item.title} -> Technology & Innovation`
    );
    return "Technology & Innovation";
  }

  if (
    categoryLower.includes("finance") ||
    categoryLower.includes("investment") ||
    categoryLower.includes("money")
  ) {
    console.log(
      `💰 Finance content detected: ${item.title} -> Finance & Investment`
    );
    return "Finance & Investment";
  }

  if (categoryLower.includes("marketing") || categoryLower.includes("brand")) {
    console.log(
      `📢 Marketing content detected: ${item.title} -> Marketing & Branding`
    );
    return "Marketing & Branding";
  }

  if (
    categoryLower.includes("education") ||
    categoryLower.includes("learning") ||
    categoryLower.includes("elearn")
  ) {
    console.log(
      `🎓 Education content detected: ${item.title} -> Education & eLearning`
    );
    return "Education & eLearning";
  }

  // 4. If no match found, return the original category or default
  console.log(
    `❓ Unknown category: ${currentCategory} for ${item.title} - keeping as is`
  );
  return currentCategory || "Business & Entrepreneurship"; // Default fallback
}

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
});

function initializeApp() {
  console.log("🚀 Initializing Admin App...");

  // Check authentication state
  onAuthStateChanged(auth, async (user) => {
    console.log("🔄 Auth state changed:", user ? user.email : "No user");

    if (user) {
      console.log("✅ User authenticated:", user.email, "UID:", user.uid);
      await checkUserRole(user);
    } else {
      console.log("❌ No authenticated user, showing login form");
      showLoginForm();
    }
  });

  // Setup login form
  document.getElementById("loginForm").addEventListener("submit", handleLogin);

  // Populate category dropdowns
  populateCategoryDropdowns();
}

async function checkUserRole(user) {
  try {
    console.log("🔍 === CHECKING USER ROLE v2.3 ===");
    console.log("📧 User email:", user.email);
    console.log("🆔 Auth UID:", user.uid);

    // Skip UID lookup and go directly to email-based search
    // This avoids the "Invalid token in path" error
    console.log("� Finding user by email (primary method)...");
    const usersRef = ref(database, "users");
    console.log("📡 Fetching all users from database...");
    const allUsersSnapshot = await get(usersRef);
    console.log("📊 All users snapshot exists:", allUsersSnapshot.exists());

    if (allUsersSnapshot.exists()) {
      const allUsers = allUsersSnapshot.val();
      console.log("👥 Total users in database:", Object.keys(allUsers).length);
      console.log("🔍 All user UIDs:", Object.keys(allUsers));

      let foundUser = null;
      let foundUID = null;

      for (const [uid, userData] of Object.entries(allUsers)) {
        console.log(`🔍 Checking UID ${uid}:`, userData?.email);
        if (
          userData &&
          userData.email &&
          userData.email.toLowerCase() === user.email.toLowerCase()
        ) {
          foundUser = userData;
          foundUID = uid;
          console.log("✅ Found user by email with UID:", uid);
          console.log("📊 User data:", userData);
          break;
        }
      }

      if (foundUser) {
        const role = foundUser.role;
        console.log("🎭 Found user role:", role);

        if (role === "super_admin") {
          console.log("👑 SUPER ADMIN ACCESS GRANTED!");
          currentUser = user;
          currentUserRole = "super_admin";
          showAdminDashboard();
        } else if (role === "admin") {
          console.log("🔧 ADMIN ACCESS GRANTED!");
          currentUser = user;
          currentUserRole = "admin";
          showAdminDashboard();
        } else if (role === "user") {
          console.log("❌ ACCESS DENIED - User role detected");
          showError(
            "Access denied. You are a regular user. Only admins and super admins can access this panel."
          );
          await signOut(auth);
        } else {
          console.log("❌ ACCESS DENIED - Unknown role:", role);
          showError(
            `Access denied. Unknown role: "${role}". Please contact the super admin.`
          );
          await signOut(auth);
        }
      } else {
        console.log("❌ No user found with email:", user.email);
        showError(
          "User not found in database. Please ensure you have signed up using the mobile app first."
        );
        await signOut(auth);
      }
    } else {
      console.log("❌ No users found in database at all");
      showError(
        "Database error. No users found. Please contact the super admin."
      );
      await signOut(auth);
    }
  } catch (error) {
    console.error("💥 ERROR checking user role:", error);
    console.error("📝 Error details:", error.message);
    console.error("📚 Error stack:", error.stack);
    console.error("🔧 Error name:", error.name);
    console.error("🔧 Error code:", error.code);
    showError(
      `Error verifying user permissions: ${error.message}. Please try again.`
    );
    await signOut(auth);
  }
}

function showLoginForm() {
  document.getElementById("loginContainer").style.display = "block";
  document.getElementById("adminDashboard").style.display = "none";
  document.getElementById("userDropdown").style.display = "none";
}

function showAdminDashboard() {
  document.getElementById("loginContainer").style.display = "none";
  document.getElementById("adminDashboard").style.display = "block";
  document.getElementById("userDropdown").style.display = "block";
  document.getElementById("userEmail").textContent = currentUser.email;

  console.log("Setting up dashboard for role:", currentUserRole);

  // Show/hide sections based on role
  if (currentUserRole === "super_admin") {
    // Super Admin: Can manage users, admins, and content
    document.getElementById("usersLink").style.display = "block";
    document.getElementById("adminsLink").style.display = "block";
    console.log("Super admin access granted - showing all sections");
  } else if (currentUserRole === "admin") {
    // Admin: Can only manage content
    document.getElementById("usersLink").style.display = "none";
    document.getElementById("adminsLink").style.display = "none";
    console.log("Admin access granted - hiding user/admin management");
  } else {
    // Fallback: Hide all admin sections
    document.getElementById("usersLink").style.display = "none";
    document.getElementById("adminsLink").style.display = "none";
    console.log("Limited access - hiding admin sections");
  }

  // Load dashboard data
  loadDashboardData();
  showSection("dashboard");
}

// Check if user exists in Realtime Database
async function checkUserExistsInDatabase(email) {
  try {
    console.log("=== CHECKING USER EXISTS IN DATABASE ===");
    console.log("Looking for email:", email);

    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);

    if (snapshot.exists()) {
      const users = snapshot.val();
      console.log("Database users found:", Object.keys(users).length);
      console.log("Database user UIDs:", Object.keys(users));

      // Check if any user has this email
      for (const [uid, userData] of Object.entries(users)) {
        console.log(`Checking UID ${uid}:`, userData.email);
        if (
          userData &&
          userData.email &&
          userData.email.toLowerCase() === email.toLowerCase()
        ) {
          console.log("✅ USER FOUND in database:", userData);
          return true;
        }
      }
    }
    console.log("❌ USER NOT FOUND in database:", email);
    return false;
  } catch (error) {
    console.error("❌ ERROR checking user in database:", error);
    return false;
  }
}

async function handleLogin(e) {
  e.preventDefault();

  const email = document.getElementById("email").value;
  const password = document.getElementById("password").value;
  const errorDiv = document.getElementById("loginError");

  try {
    errorDiv.style.display = "none";

    // First check if user exists in our database
    const userExists = await checkUserExistsInDatabase(email);
    if (!userExists) {
      showError(
        "Account not found in our system. Please sign up first using the mobile app, then try logging in again."
      );
      return;
    }

    // If user exists in database, proceed with Firebase Authentication
    await signInWithEmailAndPassword(auth, email, password);
  } catch (error) {
    console.error("Login error:", error);
    let errorMessage = "Login failed. Please try again.";

    switch (error.code) {
      case "auth/user-not-found":
        errorMessage =
          "No account found with this email address. Please sign up first using the mobile app.";
        break;
      case "auth/wrong-password":
        errorMessage = "Incorrect password. Please try again.";
        break;
      case "auth/invalid-email":
        errorMessage = "Invalid email address.";
        break;
      case "auth/too-many-requests":
        errorMessage = "Too many failed attempts. Please try again later.";
        break;
      case "auth/invalid-credential":
        errorMessage =
          "Invalid email or password. Please check your credentials or sign up first using the mobile app.";
        break;
    }

    showError(errorMessage);
  }
}

// Google Sign-In function
async function signInWithGoogle() {
  try {
    console.log("🚀 Starting Google sign-in...");

    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({
      prompt: "select_account",
    });

    // Sign in with Google
    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    console.log("✅ Google sign-in successful!");
    console.log("📧 User email:", user.email);
    console.log("🆔 User UID:", user.uid);
    console.log("🔄 Authentication will be handled by onAuthStateChanged...");

    // The onAuthStateChanged listener will automatically trigger
    // and call checkUserRole(user) to verify permissions
  } catch (error) {
    console.error("💥 Google sign-in error:", error);
    console.error("📝 Error code:", error.code);
    console.error("📝 Error message:", error.message);

    let errorMessage = "Google sign-in failed. Please try again.";

    switch (error.code) {
      case "auth/popup-closed-by-user":
        errorMessage = "Sign-in was cancelled. Please try again.";
        break;
      case "auth/popup-blocked":
        errorMessage =
          "Pop-up was blocked by your browser. Please allow pop-ups and try again.";
        break;
      case "auth/cancelled-popup-request":
        errorMessage = "Sign-in was cancelled. Please try again.";
        break;
      case "auth/network-request-failed":
        errorMessage =
          "Network error. Please check your internet connection and try again.";
        break;
      default:
        errorMessage = `Google sign-in failed: ${error.message}. Please try again or use email login.`;
    }

    showError(errorMessage);
  }
}

// Make signInWithGoogle globally available
window.signInWithGoogle = signInWithGoogle;

function showError(message) {
  const errorDiv = document.getElementById("loginError");
  errorDiv.textContent = message;
  errorDiv.style.display = "block";
}

async function logout() {
  try {
    await signOut(auth);
    showLoginForm();
  } catch (error) {
    console.error("Logout error:", error);
  }
}

function showSection(sectionName) {
  console.log(
    `Attempting to show section: ${sectionName}, User role: ${currentUserRole}`
  );

  // Check role-based access before showing sections
  if (sectionName === "users" && currentUserRole !== "super_admin") {
    console.log("Access denied to users section - not super admin");
    alert("Access denied. Only super admins can manage users.");
    return;
  }

  if (sectionName === "admins" && currentUserRole !== "super_admin") {
    console.log("Access denied to admins section - not super admin");
    alert("Access denied. Only super admins can manage admins.");
    return;
  }

  // Hide all sections
  const sections = document.querySelectorAll(".content-section");
  sections.forEach((section) => (section.style.display = "none"));

  // Remove active class from all nav items
  const navItems = document.querySelectorAll(".list-group-item");
  navItems.forEach((item) => item.classList.remove("active"));

  // Show selected section
  document.getElementById(sectionName + "Section").style.display = "block";

  // Add active class to nav item - SIMPLIFIED VERSION
  try {
    // Try to find the nav item and make it active
    const navItems = document.querySelectorAll(".list-group-item");
    navItems.forEach((item) => {
      if (
        item.textContent.toLowerCase().includes(sectionName.toLowerCase()) ||
        item.getAttribute("onclick") === `showSection('${sectionName}')`
      ) {
        item.classList.add("active");
      }
    });
  } catch (navError) {
    console.log("Nav activation error (non-critical):", navError);
    // Continue anyway - this is not critical for functionality
  }

  console.log(`Successfully showing section: ${sectionName}`);

  // Load section data
  switch (sectionName) {
    case "dashboard":
      loadDashboardData();
      break;
    case "content":
      loadContentData();
      break;
    case "users":
      if (currentUserRole === "super_admin") {
        loadUsersData();
      } else {
        console.log("Blocked users data loading - insufficient permissions");
      }
      break;
    case "admins":
      if (currentUserRole === "super_admin") {
        loadAdminsData();
      } else {
        console.log("Blocked admins data loading - insufficient permissions");
      }
      break;
  }
}

async function loadDashboardData() {
  try {
    // Load content count (handle nested structure)
    const contentRef = ref(database, "content");
    const contentSnapshot = await get(contentRef);
    let contentCount = 0;

    if (contentSnapshot.exists()) {
      const content = contentSnapshot.val();

      // Count content items in nested structure
      Object.entries(content).forEach(([key, value]) => {
        if (value && typeof value === "object") {
          if (value.title && value.description) {
            // Direct content item (flat structure)
            contentCount++;
          } else {
            // Category structure - count items in this category
            Object.entries(value).forEach(([itemId, item]) => {
              if (item && typeof item === "object" && item.title) {
                contentCount++;
              }
            });
          }
        }
      });
    }

    document.getElementById("totalContent").textContent = contentCount;

    // Load users count
    const usersRef = ref(database, "users");
    const usersSnapshot = await get(usersRef);
    let userCount = 0;
    let adminCount = 0;

    if (usersSnapshot.exists()) {
      const users = usersSnapshot.val();
      Object.values(users).forEach((user) => {
        if (user.role === "admin" || user.role === "super_admin") {
          adminCount++;
        } else {
          userCount++;
        }
      });
    }

    document.getElementById("totalUsers").textContent = userCount;
    document.getElementById("totalAdmins").textContent = adminCount;
  } catch (error) {
    console.error("Error loading dashboard data:", error);
  }
}

function populateCategoryDropdowns() {
  const categorySelects = [
    "categoryFilter",
    "contentCategory",
    "editContentCategory",
  ];

  categorySelects.forEach((selectId) => {
    const select = document.getElementById(selectId);
    if (select) {
      // Clear existing options (except first one for filter)
      if (selectId === "categoryFilter") {
        select.innerHTML = '<option value="">All Categories</option>';
      } else {
        select.innerHTML = '<option value="">Select Category</option>';
      }

      // Add category options
      CATEGORIES.forEach((category) => {
        const option = document.createElement("option");
        option.value = category;
        option.textContent = category;
        select.appendChild(option);
      });
    }
  });
}

// Content Management Functions
async function loadContentData() {
  try {
    console.log("🔄 Loading content data...");
    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);
    const tableBody = document.getElementById("contentTableBody");

    tableBody.innerHTML = "";

    if (snapshot.exists()) {
      const content = snapshot.val();
      console.log("📊 Content structure:", content);

      // Check if content is organized by category (nested structure)
      let allContentItems = [];

      Object.entries(content).forEach(([key, value]) => {
        if (value && typeof value === "object") {
          // Check if this is a category (contains nested items) or direct content item
          if (value.title && value.description) {
            // Direct content item (flat structure)
            const correctCategory = getCorrectCategory(value);
            allContentItems.push({
              id: key,
              ...value,
              category: correctCategory,
              originalCategory: value.category,
            });
          } else {
            // Category structure - iterate through items in this category
            Object.entries(value).forEach(([itemId, item]) => {
              if (item && typeof item === "object" && item.title) {
                // Use smart category detection
                const itemForDetection = {
                  ...item,
                  category: item.category || key, // Use item category or folder name
                };
                const correctCategory = getCorrectCategory(itemForDetection);

                console.log(`🔍 Smart detection for "${item.title}":`, {
                  original: item.category || key,
                  detected: correctCategory,
                  fileType: item.fileType,
                  fileName: item.fileName,
                });

                allContentItems.push({
                  id: itemId,
                  category: correctCategory, // Use smart-detected category
                  storageCategory: key, // Keep original for storage operations
                  originalCategory: item.category || key, // Track original
                  ...item,
                });
              }
            });
          }
        }
      });

      console.log("📋 Total content items found:", allContentItems.length);
      console.log("📋 Content items:", allContentItems);

      // Create rows for all content items
      allContentItems.forEach((item) => {
        const row = createContentRow(item.id, item);
        tableBody.appendChild(row);
      });

      if (allContentItems.length === 0) {
        tableBody.innerHTML =
          '<tr><td colspan="6" class="text-center">No content found</td></tr>';
      }
    } else {
      console.log("❌ No content found in database");
      tableBody.innerHTML =
        '<tr><td colspan="6" class="text-center">No content found</td></tr>';
    }
  } catch (error) {
    console.error("💥 Error loading content:", error);
    const tableBody = document.getElementById("contentTableBody");
    tableBody.innerHTML =
      '<tr><td colspan="6" class="text-center text-danger">Error loading content</td></tr>';
  }
}

function createContentRow(key, item) {
  const row = document.createElement("tr");
  const fileSize = item.fileSize ? formatFileSize(item.fileSize) : "Unknown";
  const createdDate = item.createdAt
    ? new Date(item.createdAt).toLocaleDateString()
    : "Unknown";
  const fileIcon = getFileIcon(item.fileType);

  row.innerHTML = `
        <td>
            <i class="${fileIcon} file-icon"></i>
            ${item.title}
        </td>
        <td><span class="badge bg-primary">${item.category}</span></td>
        <td>${item.fileType || "Unknown"}</td>
        <td>${fileSize}</td>
        <td>${createdDate}</td>
        <td>
            <button class="btn btn-sm btn-warning" onclick="editContent('${key}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger" onclick="deleteContent('${key}')">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

  return row;
}

function getFileIcon(fileType) {
  if (!fileType) return "fas fa-file file-default";

  const type = fileType.toLowerCase();
  if (type.includes("pdf")) return "fas fa-file-pdf file-pdf";
  if (type.includes("doc") || type.includes("docx"))
    return "fas fa-file-word file-doc";
  if (type.includes("video") || type.includes("mp4"))
    return "fas fa-file-video file-video";
  if (type.includes("image") || type.includes("jpg") || type.includes("png"))
    return "fas fa-file-image file-image";
  if (type.includes("zip") || type.includes("rar"))
    return "fas fa-file-archive file-archive";
  return "fas fa-file file-default";
}

function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function showAddContentModal() {
  const modal = new bootstrap.Modal(document.getElementById("addContentModal"));
  modal.show();
}

async function addContent() {
  const title = document.getElementById("contentTitle").value;
  const description = document.getElementById("contentDescription").value;
  const fullCategory = document.getElementById("contentCategory").value; // Full category name
  const fileInput = document.getElementById("contentFile");
  const file = fileInput.files[0];

  if (!title || !description || !fullCategory || !file) {
    alert("Please fill in all fields and select a file.");
    return;
  }

  // Get the storage folder name (short name) for this category
  const storageCategory =
    REVERSE_CATEGORY_MAPPING[fullCategory] || fullCategory;

  try {
    // Show progress
    const progressDiv = document.getElementById("uploadProgress");
    const progressBar = progressDiv.querySelector(".progress-bar");
    progressDiv.style.display = "block";

    // Upload file to storage using the storage category (short name)
    const fileName = `${Date.now()}_${file.name}`;
    const fileRef = storageRef(
      storage,
      `content/${storageCategory}/${fileName}`
    );
    const uploadTask = uploadBytesResumable(fileRef, file);

    console.log(`📤 Uploading to: content/${storageCategory}/${fileName}`);

    uploadTask.on(
      "state_changed",
      (snapshot) => {
        const progress =
          (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        progressBar.style.width = progress + "%";
      },
      (error) => {
        console.error("Upload error:", error);
        alert("Upload failed. Please try again.");
        progressDiv.style.display = "none";
      },
      async () => {
        try {
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

          // Save metadata to database in the nested structure
          const contentData = {
            title: title,
            description: description,
            category: fullCategory, // Store full category name
            storageCategory: storageCategory, // Store storage folder name
            fileName: fileName,
            originalFileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            downloadURL: downloadURL,
            createdAt: new Date().toISOString(),
            createdBy: currentUser.email,
          };

          // Store in nested structure: content/{storageCategory}/{contentId}
          const categoryRef = ref(database, `content/${storageCategory}`);
          await push(categoryRef, contentData);

          console.log(`💾 Saved to database: content/${storageCategory}/`);

          // Close modal and refresh content
          bootstrap.Modal.getInstance(
            document.getElementById("addContentModal")
          ).hide();
          document.getElementById("addContentForm").reset();
          progressDiv.style.display = "none";
          progressBar.style.width = "0%";

          loadContentData();
          loadDashboardData();

          alert("Content added successfully!");
        } catch (error) {
          console.error("Error saving content metadata:", error);
          alert("Error saving content. Please try again.");
        }
      }
    );
  } catch (error) {
    console.error("Error adding content:", error);
    alert("Error adding content. Please try again.");
  }
}

function filterContentByCategory() {
  const selectedCategory = document.getElementById("categoryFilter").value;
  const rows = document.querySelectorAll("#contentTableBody tr");

  console.log(`🔍 Filtering by category: "${selectedCategory}"`);

  rows.forEach((row) => {
    const categoryCell = row.querySelector("td:nth-child(2)");
    if (categoryCell) {
      // Get the category text from the badge
      const categoryBadge = categoryCell.querySelector(".badge");
      const category = categoryBadge
        ? categoryBadge.textContent.trim()
        : categoryCell.textContent.trim();

      console.log(`📋 Row category: "${category}"`);

      // Create mapping from database categories to display categories
      const categoryMapping = {
        Business: "Business & Entrepreneurship",
        Health: "Health & Wellness",
        Technology: "Technology & Innovation",
        Recipes: "Recipes & Nutrition",
        MRR: "MRR Video Courses",
        Finance: "Finance & Investment",
        SelfImprovement: "Self-Improvement & Motivation",
        Marketing: "Marketing & Branding",
        Design: "Design & Templates",
        Spirituality: "Spirituality & Mindfulness",
        Career: "Career & Freelancing",
        AI: "AI & Automation",
        Education: "Education & eLearning",
        Legal: "Legal & Business Docs",
        eCommerce: "eCommerce & Dropshipping",
        Parenting: "Parenting & Family",
        Fashion: "Fashion & Beauty",
        Travel: "Travel & Lifestyle",
        Kids: "Kids & Learning",
        Entertainment: "Entertainment & Fun",
      };

      // Check if we should show this row
      let shouldShow = false;

      // FIRST: Check if this is video content - videos should ONLY show in MRR Video Courses
      const titleCell = row.querySelector("td:nth-child(1)");
      const fileTypeCell = row.querySelector("td:nth-child(3)");

      if (titleCell && fileTypeCell) {
        const title = titleCell.textContent.trim();
        const fileType = fileTypeCell.textContent.trim().toLowerCase();

        const videoTypes = [
          "mp4",
          "avi",
          "mov",
          "wmv",
          "flv",
          "webm",
          "mkv",
          "m4v",
        ];
        const isVideo = videoTypes.some(
          (type) =>
            fileType.includes(type) || title.toLowerCase().includes(`.${type}`)
        );

        // If this is video content and we're NOT in MRR Video Courses, hide it immediately
        if (isVideo && selectedCategory !== "MRR Video Courses") {
          console.log(
            `🚫 Video content "${title}" blocked from "${selectedCategory}" - videos only in MRR Video Courses`
          );
          shouldShow = false;
          row.style.display = "none";
          return; // Exit early, don't process this row further
        }
      }

      if (!selectedCategory || selectedCategory === "All Categories") {
        shouldShow = true;
      } else {
        // Special case: ALL videos should show in "MRR Video Courses"
        if (selectedCategory === "MRR Video Courses") {
          // Check if this row contains video content
          const titleCell = row.querySelector("td:nth-child(1)");
          const fileTypeCell = row.querySelector("td:nth-child(3)");

          if (titleCell && fileTypeCell) {
            const title = titleCell.textContent.trim();
            const fileType = fileTypeCell.textContent.trim().toLowerCase();

            const videoTypes = [
              "mp4",
              "avi",
              "mov",
              "wmv",
              "flv",
              "webm",
              "mkv",
              "m4v",
            ];
            const isVideo = videoTypes.some(
              (type) =>
                fileType.includes(type) ||
                title.toLowerCase().includes(`.${type}`)
            );

            if (isVideo) {
              console.log(
                `📹 Video detected in filter: "${title}" -> showing in MRR Video Courses`
              );
              shouldShow = true;
            }
          }
        }

        // Special case: Recipe content should show in "Recipes & Nutrition"
        if (selectedCategory === "Recipes & Nutrition" && !shouldShow) {
          const titleCell = row.querySelector("td:nth-child(1)");
          if (titleCell) {
            const title = titleCell.textContent.trim().toLowerCase();
            const recipeKeywords = [
              "recipe",
              "cooking",
              "food",
              "nutrition",
              "meal",
              "diet",
              "kitchen",
              "ingredient",
              "cook",
            ];

            const hasRecipeKeyword = recipeKeywords.some((keyword) =>
              title.includes(keyword)
            );
            if (hasRecipeKeyword) {
              console.log(
                `🍳 Recipe content detected in filter: "${title}" -> showing in Recipes & Nutrition`
              );
              shouldShow = true;
            }
          }
        }

        // Regular category matching (if not already shown)
        if (!shouldShow) {
          // Direct match
          if (category === selectedCategory) {
            shouldShow = true;
          } else {
            // Check if the stored category maps to the selected display category
            const mappedCategory = categoryMapping[category];
            if (mappedCategory === selectedCategory) {
              console.log(
                `✅ Category mapping match: "${category}" -> "${mappedCategory}"`
              );
              shouldShow = true;
            }
          }
        }
      }

      if (shouldShow) {
        row.style.display = "";
      } else {
        row.style.display = "none";
      }
    }
  });
}

async function editContent(contentId) {
  try {
    console.log("✏️ Editing content with ID:", contentId);

    // Find the content item in the nested structure
    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);

    if (!snapshot.exists()) {
      alert("Content not found.");
      return;
    }

    const content = snapshot.val();
    let contentData = null;
    let contentPath = null;

    // Search through the nested structure
    for (const [categoryKey, categoryValue] of Object.entries(content)) {
      if (categoryValue && typeof categoryValue === "object") {
        if (
          categoryValue.title &&
          categoryValue.description &&
          categoryKey === contentId
        ) {
          // Direct content item (flat structure)
          contentData = categoryValue;
          contentPath = `content/${contentId}`;
          break;
        } else {
          // Category structure - search within category
          for (const [itemId, item] of Object.entries(categoryValue)) {
            if (
              itemId === contentId &&
              item &&
              typeof item === "object" &&
              item.title
            ) {
              contentData = item;
              contentPath = `content/${categoryKey}/${itemId}`;
              contentData.storageCategory = categoryKey; // Add storage category info
              break;
            }
          }
          if (contentData) break;
        }
      }
    }

    if (!contentData) {
      alert("Content not found in database.");
      return;
    }

    console.log("📄 Found content data:", contentData);

    // Populate edit modal with current data
    document.getElementById("editContentId").value = contentId;
    document.getElementById("editContentTitle").value = contentData.title || "";
    document.getElementById("editContentDescription").value =
      contentData.description || "";

    // Set category - use full category name
    const fullCategory =
      CATEGORY_MAPPING[contentData.storageCategory] || contentData.category;
    document.getElementById("editContentCategory").value = fullCategory;

    // Show current file info
    document.getElementById("currentFileName").textContent =
      contentData.originalFileName || contentData.fileName || "Unknown";

    // Show edit modal
    const modal = new bootstrap.Modal(
      document.getElementById("editContentModal")
    );
    modal.show();
  } catch (error) {
    console.error("💥 Error loading content for edit:", error);
    alert("Error loading content. Please try again.");
  }
}

async function updateContent() {
  const contentId = document.getElementById("editContentId").value;
  const title = document.getElementById("editContentTitle").value;
  const description = document.getElementById("editContentDescription").value;
  const fullCategory = document.getElementById("editContentCategory").value;
  const fileInput = document.getElementById("editContentFile");
  const newFile = fileInput.files[0];

  if (!title || !description || !fullCategory) {
    alert("Please fill in all required fields.");
    return;
  }

  try {
    console.log("🔄 Updating content:", contentId);

    // Find the current content data
    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);

    if (!snapshot.exists()) {
      alert("Content not found.");
      return;
    }

    const content = snapshot.val();
    let currentContentData = null;
    let currentContentPath = null;
    let currentStorageCategory = null;

    // Search through the nested structure
    for (const [categoryKey, categoryValue] of Object.entries(content)) {
      if (categoryValue && typeof categoryValue === "object") {
        if (
          categoryValue.title &&
          categoryValue.description &&
          categoryKey === contentId
        ) {
          // Direct content item (flat structure)
          currentContentData = categoryValue;
          currentContentPath = `content/${contentId}`;
          currentStorageCategory = categoryKey;
          break;
        } else {
          // Category structure - search within category
          for (const [itemId, item] of Object.entries(categoryValue)) {
            if (
              itemId === contentId &&
              item &&
              typeof item === "object" &&
              item.title
            ) {
              currentContentData = item;
              currentContentPath = `content/${categoryKey}/${itemId}`;
              currentStorageCategory = categoryKey;
              break;
            }
          }
          if (currentContentData) break;
        }
      }
    }

    if (!currentContentData) {
      alert("Content not found in database.");
      return;
    }

    // Get storage categories
    const newStorageCategory =
      REVERSE_CATEGORY_MAPPING[fullCategory] || fullCategory;
    const categoryChanged = currentStorageCategory !== newStorageCategory;

    let downloadURL = currentContentData.downloadURL;
    let fileName = currentContentData.fileName;
    let fileSize = currentContentData.fileSize;
    let fileType = currentContentData.fileType;
    let originalFileName = currentContentData.originalFileName;

    // Handle file replacement or category change
    if (newFile || categoryChanged) {
      const progressDiv = document.getElementById("editUploadProgress");
      const progressBar = progressDiv.querySelector(".progress-bar");
      progressDiv.style.display = "block";

      if (newFile) {
        // New file uploaded
        fileName = `${Date.now()}_${newFile.name}`;
        fileSize = newFile.size;
        fileType = newFile.type;
        originalFileName = newFile.name;
      }

      // Upload to new location
      const fileRef = storageRef(
        storage,
        `content/${newStorageCategory}/${fileName}`
      );

      if (newFile) {
        // Upload new file
        const uploadTask = uploadBytesResumable(fileRef, newFile);

        await new Promise((resolve, reject) => {
          uploadTask.on(
            "state_changed",
            (snapshot) => {
              const progress =
                (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
              progressBar.style.width = progress + "%";
            },
            (error) => {
              console.error("Upload error:", error);
              reject(error);
            },
            async () => {
              downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve();
            }
          );
        });
      } else if (categoryChanged) {
        // Copy existing file to new category
        const oldFileRef = storageRef(
          storage,
          `content/${currentStorageCategory}/${fileName}`
        );
        const fileBlob = await getBlob(oldFileRef);
        await uploadBytes(fileRef, fileBlob);
        downloadURL = await getDownloadURL(fileRef);
      }

      progressDiv.style.display = "none";
    }

    // Update database
    const updatedContentData = {
      ...currentContentData,
      title: title,
      description: description,
      category: fullCategory,
      storageCategory: newStorageCategory,
      fileName: fileName,
      originalFileName: originalFileName,
      fileType: fileType,
      fileSize: fileSize,
      downloadURL: downloadURL,
      updatedAt: new Date().toISOString(),
      updatedBy: currentUser.email,
    };

    // If category changed, move the content
    if (categoryChanged) {
      // Add to new location
      const newCategoryRef = ref(database, `content/${newStorageCategory}`);
      await push(newCategoryRef, updatedContentData);

      // Remove from old location
      const oldItemRef = ref(database, currentContentPath);
      await remove(oldItemRef);

      // Delete old file if category changed and we uploaded a new file or moved it
      if (currentContentData.fileName) {
        try {
          const oldFileRef = storageRef(
            storage,
            `content/${currentStorageCategory}/${currentContentData.fileName}`
          );
          await deleteObject(oldFileRef);
        } catch (error) {
          console.warn("Could not delete old file:", error);
        }
      }
    } else {
      // Update in same location
      const itemRef = ref(database, currentContentPath);
      await update(itemRef, updatedContentData);

      // Delete old file if we uploaded a new one
      if (newFile && currentContentData.fileName !== fileName) {
        try {
          const oldFileRef = storageRef(
            storage,
            `content/${currentStorageCategory}/${currentContentData.fileName}`
          );
          await deleteObject(oldFileRef);
        } catch (error) {
          console.warn("Could not delete old file:", error);
        }
      }
    }

    // Close modal and refresh
    bootstrap.Modal.getInstance(
      document.getElementById("editContentModal")
    ).hide();
    document.getElementById("editContentForm").reset();

    loadContentData();
    loadDashboardData();

    alert("Content updated successfully!");
  } catch (error) {
    console.error("💥 Error updating content:", error);
    alert("Error updating content. Please try again.");
  }
}

// Function to fix/migrate all content categories
async function fixAllCategories() {
  if (
    !confirm(
      "This will analyze and fix all content categories based on file types and content. This may take a while. Continue?"
    )
  ) {
    return;
  }

  try {
    console.log("🔧 Starting category fix process...");

    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);

    if (!snapshot.exists()) {
      alert("No content found to fix.");
      return;
    }

    const content = snapshot.val();
    let fixedCount = 0;
    let totalCount = 0;

    // Show progress
    const progressDiv = document.createElement("div");
    progressDiv.innerHTML = `
      <div class="alert alert-info">
        <i class="fas fa-cog fa-spin me-2"></i>
        Fixing categories... <span id="fixProgress">0/0</span>
      </div>
    `;
    document.body.appendChild(progressDiv);

    // Process all content
    for (const [categoryKey, categoryValue] of Object.entries(content)) {
      if (categoryValue && typeof categoryValue === "object") {
        if (categoryValue.title && categoryValue.description) {
          // Direct content item (flat structure)
          totalCount++;
          const correctCategory = getCorrectCategory(categoryValue);
          const correctStorageCategory =
            REVERSE_CATEGORY_MAPPING[correctCategory] || correctCategory;

          if (
            correctCategory !== categoryValue.category ||
            categoryKey !== correctStorageCategory
          ) {
            console.log(
              `🔧 Fixing: ${categoryValue.title} -> ${correctCategory}`
            );

            // Update the item
            const updatedData = {
              ...categoryValue,
              category: correctCategory,
              storageCategory: correctStorageCategory,
              fixedAt: new Date().toISOString(),
              fixedBy: currentUser.email,
            };

            // If storage category changed, move the content
            if (categoryKey !== correctStorageCategory) {
              // Add to new location
              const newCategoryRef = ref(
                database,
                `content/${correctStorageCategory}`
              );
              await push(newCategoryRef, updatedData);

              // Remove from old location
              const oldItemRef = ref(database, `content/${categoryKey}`);
              await remove(oldItemRef);

              // Move file in storage if needed
              if (categoryValue.fileName) {
                try {
                  const oldFileRef = storageRef(
                    storage,
                    `content/${categoryKey}/${categoryValue.fileName}`
                  );
                  const newFileRef = storageRef(
                    storage,
                    `content/${correctStorageCategory}/${categoryValue.fileName}`
                  );

                  const fileBlob = await getBlob(oldFileRef);
                  await uploadBytes(newFileRef, fileBlob);
                  await deleteObject(oldFileRef);

                  // Update download URL
                  updatedData.downloadURL = await getDownloadURL(newFileRef);
                  const itemRef = ref(
                    database,
                    `content/${correctStorageCategory}`
                  );
                  await update(itemRef, {
                    downloadURL: updatedData.downloadURL,
                  });
                } catch (storageError) {
                  console.warn("Could not move file in storage:", storageError);
                }
              }
            } else {
              // Update in same location
              const itemRef = ref(database, `content/${categoryKey}`);
              await update(itemRef, updatedData);
            }

            fixedCount++;
          }
        } else {
          // Category structure - process items within category
          for (const [itemId, item] of Object.entries(categoryValue)) {
            if (item && typeof item === "object" && item.title) {
              totalCount++;
              const correctCategory = getCorrectCategory({
                ...item,
                category: item.category || categoryKey,
              });
              const correctStorageCategory =
                REVERSE_CATEGORY_MAPPING[correctCategory] || correctCategory;

              if (
                correctCategory !== item.category ||
                categoryKey !== correctStorageCategory
              ) {
                console.log(`🔧 Fixing: ${item.title} -> ${correctCategory}`);

                const updatedData = {
                  ...item,
                  category: correctCategory,
                  storageCategory: correctStorageCategory,
                  fixedAt: new Date().toISOString(),
                  fixedBy: currentUser.email,
                };

                // If storage category changed, move the content
                if (categoryKey !== correctStorageCategory) {
                  // Add to new location
                  const newCategoryRef = ref(
                    database,
                    `content/${correctStorageCategory}`
                  );
                  await push(newCategoryRef, updatedData);

                  // Remove from old location
                  const oldItemRef = ref(
                    database,
                    `content/${categoryKey}/${itemId}`
                  );
                  await remove(oldItemRef);

                  // Move file in storage if needed
                  if (item.fileName) {
                    try {
                      const oldFileRef = storageRef(
                        storage,
                        `content/${categoryKey}/${item.fileName}`
                      );
                      const newFileRef = storageRef(
                        storage,
                        `content/${correctStorageCategory}/${item.fileName}`
                      );

                      const fileBlob = await getBlob(oldFileRef);
                      await uploadBytes(newFileRef, fileBlob);
                      await deleteObject(oldFileRef);

                      // Update download URL
                      updatedData.downloadURL = await getDownloadURL(
                        newFileRef
                      );
                      const newItemRef = ref(
                        database,
                        `content/${correctStorageCategory}/${itemId}`
                      );
                      await update(newItemRef, {
                        downloadURL: updatedData.downloadURL,
                      });
                    } catch (storageError) {
                      console.warn(
                        "Could not move file in storage:",
                        storageError
                      );
                    }
                  }
                } else {
                  // Update in same location
                  const itemRef = ref(
                    database,
                    `content/${categoryKey}/${itemId}`
                  );
                  await update(itemRef, updatedData);
                }

                fixedCount++;
              }

              // Update progress
              document.getElementById(
                "fixProgress"
              ).textContent = `${fixedCount}/${totalCount}`;
            }
          }
        }
      }
    }

    // Remove progress indicator
    document.body.removeChild(progressDiv);

    // Refresh data
    loadContentData();
    loadDashboardData();

    alert(
      `Category fix complete! Fixed ${fixedCount} out of ${totalCount} items.`
    );
  } catch (error) {
    console.error("💥 Error fixing categories:", error);
    alert("Error fixing categories. Please try again.");
  }
}

async function deleteContent(contentId) {
  if (!confirm("Are you sure you want to delete this content?")) {
    return;
  }

  try {
    console.log("🗑️ Deleting content with ID:", contentId);

    // First, find the content item in the nested structure
    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);

    if (!snapshot.exists()) {
      alert("Content not found.");
      return;
    }

    const content = snapshot.val();
    let contentData = null;
    let contentPath = null;

    // Search through the nested structure
    for (const [categoryKey, categoryValue] of Object.entries(content)) {
      if (categoryValue && typeof categoryValue === "object") {
        if (
          categoryValue.title &&
          categoryValue.description &&
          categoryKey === contentId
        ) {
          // Direct content item (flat structure)
          contentData = categoryValue;
          contentPath = `content/${contentId}`;
          break;
        } else {
          // Category structure - search within category
          for (const [itemId, item] of Object.entries(categoryValue)) {
            if (
              itemId === contentId &&
              item &&
              typeof item === "object" &&
              item.title
            ) {
              contentData = item;
              contentPath = `content/${categoryKey}/${itemId}`;
              break;
            }
          }
          if (contentData) break;
        }
      }
    }

    if (!contentData || !contentPath) {
      alert("Content not found in database.");
      return;
    }

    console.log("📍 Found content at path:", contentPath);
    console.log("📄 Content data:", contentData);

    try {
      // Delete file from storage if it exists
      if (contentData.fileName) {
        // Use storageCategory if available, otherwise try to map from category
        const storageCat =
          contentData.storageCategory ||
          REVERSE_CATEGORY_MAPPING[contentData.category] ||
          contentData.category;

        const fileRef = storageRef(
          storage,
          `content/${storageCat}/${contentData.fileName}`
        );
        await deleteObject(fileRef);
        console.log(
          `🗑️ File deleted from storage: content/${storageCat}/${contentData.fileName}`
        );
      }
    } catch (storageError) {
      console.warn(
        "⚠️ Could not delete file from storage (may not exist):",
        storageError
      );
      // Continue with database deletion even if storage deletion fails
    }

    // Delete from database
    const itemRef = ref(database, contentPath);
    await remove(itemRef);
    console.log("🗑️ Content deleted from database");

    loadContentData();
    loadDashboardData();

    alert("Content deleted successfully!");
  } catch (error) {
    console.error("💥 Error deleting content:", error);
    alert("Error deleting content. Please try again.");
  }
}

// User Management Functions
async function loadUsersData() {
  try {
    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);
    const tableBody = document.getElementById("usersTableBody");

    tableBody.innerHTML = "";

    if (snapshot.exists()) {
      const users = snapshot.val();
      Object.entries(users).forEach(([uid, user]) => {
        if (user.role === "user") {
          const row = createUserRow(uid, user);
          tableBody.appendChild(row);
        }
      });
    }
  } catch (error) {
    console.error("Error loading users:", error);
  }
}

function createUserRow(uid, user) {
  const row = document.createElement("tr");
  const createdDate = user.createdAt
    ? new Date(user.createdAt).toLocaleDateString()
    : "Unknown";
  const lastLogin = user.lastLoginAt
    ? new Date(user.lastLoginAt).toLocaleDateString()
    : "Never";

  row.innerHTML = `
        <td>${user.email}</td>
        <td>${user.displayName || "N/A"}</td>
        <td><span class="badge bg-success">${user.role}</span></td>
        <td><span class="badge bg-info">${
          user.signUpMethod || "email"
        }</span></td>
        <td>${createdDate}</td>
        <td>${lastLogin}</td>
        <td>
            <button class="btn btn-sm btn-warning" onclick="makeAdmin('${uid}')">
                <i class="fas fa-user-shield"></i> Make Admin
            </button>
        </td>
    `;

  return row;
}

async function makeAdmin(uid) {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can promote users to admin.");
    return;
  }

  try {
    // Get current user data to check their current role
    const userRef = ref(database, `users/${uid}`);
    const snapshot = await get(userRef);

    if (snapshot.exists()) {
      const userData = snapshot.val();

      if (userData.role === "admin") {
        alert("This user is already an admin.");
        return;
      }

      if (userData.role === "super_admin") {
        alert("This user is already a super admin.");
        return;
      }

      if (
        !confirm(
          `Are you sure you want to promote "${userData.email}" from "${userData.role}" to "admin"?`
        )
      ) {
        return;
      }

      // Update user role to admin
      await update(userRef, { role: "admin" });

      loadUsersData();
      loadAdminsData();
      loadDashboardData();

      alert(`User "${userData.email}" promoted to admin successfully!`);
    } else {
      alert("User not found in database.");
    }
  } catch (error) {
    console.error("Error making user admin:", error);
    alert("Error promoting user. Please try again.");
  }
}

// Admin Management Functions
async function loadAdminsData() {
  try {
    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);
    const tableBody = document.getElementById("adminsTableBody");

    tableBody.innerHTML = "";

    if (snapshot.exists()) {
      const users = snapshot.val();
      Object.entries(users).forEach(([uid, user]) => {
        if (user.role === "admin" || user.role === "super_admin") {
          const row = createAdminRow(uid, user);
          tableBody.appendChild(row);
        }
      });
    }
  } catch (error) {
    console.error("Error loading admins:", error);
  }
}

function createAdminRow(uid, user) {
  const row = document.createElement("tr");
  const createdDate = user.createdAt
    ? new Date(user.createdAt).toLocaleDateString()
    : "Unknown";
  const lastLogin = user.lastLoginAt
    ? new Date(user.lastLoginAt).toLocaleDateString()
    : "Never";
  const isSuperAdmin = user.role === "super_admin";

  row.innerHTML = `
        <td>${user.email}</td>
        <td>${user.displayName || "N/A"}</td>
        <td>${createdDate}</td>
        <td>${lastLogin}</td>
        <td>
            ${
              !isSuperAdmin
                ? `
                <button class="btn btn-sm btn-danger" onclick="removeAdmin('${uid}')">
                    <i class="fas fa-user-minus"></i> Remove Admin
                </button>
            `
                : '<span class="badge bg-warning">Super Admin</span>'
            }
        </td>
    `;

  return row;
}

function showAddAdminModal() {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can add new admins.");
    return;
  }

  const modal = new bootstrap.Modal(document.getElementById("addAdminModal"));
  modal.show();
}

async function addAdmin() {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can add new admins.");
    return;
  }

  const email = document.getElementById("adminEmail").value.trim();

  if (!email) {
    alert("Please enter an email address.");
    return;
  }

  try {
    // Find user by email
    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);

    if (snapshot.exists()) {
      const users = snapshot.val();
      let userFound = false;

      for (const [uid, user] of Object.entries(users)) {
        if (user.email === email) {
          userFound = true;

          if (user.role === "admin") {
            alert(`"${email}" is already an admin.`);
            return;
          }

          if (user.role === "super_admin") {
            alert(`"${email}" is already a super admin.`);
            return;
          }

          // Confirm promotion
          if (
            !confirm(
              `Are you sure you want to promote "${email}" from "${user.role}" to "admin"?`
            )
          ) {
            return;
          }

          // Make user admin
          const userRef = ref(database, `users/${uid}`);
          await update(userRef, { role: "admin" });

          // Close modal and refresh
          bootstrap.Modal.getInstance(
            document.getElementById("addAdminModal")
          ).hide();
          document.getElementById("addAdminForm").reset();

          loadAdminsData();
          loadUsersData();
          loadDashboardData();

          alert(`"${email}" promoted to admin successfully!`);
          break;
        }
      }

      if (!userFound) {
        alert(
          `No user found with email "${email}". The user must be registered in the mobile app first.`
        );
      }
    } else {
      alert("No users found in the database.");
    }
  } catch (error) {
    console.error("Error adding admin:", error);
    alert("Error adding admin. Please try again.");
  }
}

async function removeAdmin(uid) {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can remove admin privileges.");
    return;
  }

  try {
    // Get current user data to check their current role
    const userRef = ref(database, `users/${uid}`);
    const snapshot = await get(userRef);

    if (snapshot.exists()) {
      const userData = snapshot.val();

      // Prevent removing super admin privileges
      if (userData.role === "super_admin") {
        alert("You cannot remove super admin privileges.");
        return;
      }

      // Prevent removing admin privileges from current user
      if (userData.email === currentUser.email) {
        alert("You cannot remove your own admin privileges.");
        return;
      }

      if (userData.role !== "admin") {
        alert("This user is not an admin.");
        return;
      }

      if (
        !confirm(
          `Are you sure you want to remove admin privileges from "${userData.email}"? They will become a regular user.`
        )
      ) {
        return;
      }

      // Update user role to user
      await update(userRef, { role: "user" });

      loadAdminsData();
      loadUsersData();
      loadDashboardData();

      alert(
        `Admin privileges removed from "${userData.email}" successfully! They are now a regular user.`
      );
    } else {
      alert("User not found in database.");
    }
  } catch (error) {
    console.error("Error removing admin:", error);
    alert("Error removing admin. Please try again.");
  }
}

// Make functions globally available
window.showSection = showSection;
window.logout = logout;
window.showAddContentModal = showAddContentModal;
window.showAddAdminModal = showAddAdminModal;
window.addContent = addContent;
window.addAdmin = addAdmin;
window.filterContentByCategory = filterContentByCategory;
window.editContent = editContent;
window.updateContent = updateContent;
window.deleteContent = deleteContent;
window.makeAdmin = makeAdmin;
window.removeAdmin = removeAdmin;
window.deleteUser = deleteUser;
