class ContentItem {
  final String id;
  final String title;
  final String description;
  final String category;
  final String fileUrl;
  final String fileName;
  final String fileType;
  final int fileSize;
  final DateTime createdAt;

  ContentItem({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.fileUrl,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    required this.createdAt,
  });

  // Convert from Firebase Realtime Database Map
  factory ContentItem.fromMap(String id, Map<dynamic, dynamic> map) {
    return ContentItem(
      id: id,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? '',
      fileUrl: map['downloadURL'] ?? map['fileUrl'] ?? '', // Admin uses downloadURL
      fileName: map['fileName'] ?? '',
      fileType: map['fileType'] ?? '',
      fileSize: map['fileSize'] ?? 0,
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  // Convert to Firebase Realtime Database Map
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'category': category,
      'fileUrl': fileUrl,
      'fileName': fileName,
      'fileType': fileType,
      'fileSize': fileSize,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Get file extension from fileName
  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  // Check if file is a PDF
  bool get isPdf => fileExtension == 'pdf';

  // Check if file is a video
  bool get isVideo => ['mp4', 'mov', 'avi', 'mkv'].contains(fileExtension);

  // Check if file is a recipe (based on title, description, or category containing "recipe")
  bool get isRecipe {
    final lowerTitle = title.toLowerCase();
    final lowerDescription = description.toLowerCase();
    final lowerCategory = category.toLowerCase();
    return lowerTitle.contains('recipe') ||
        lowerDescription.contains('recipe') ||
        lowerCategory.contains('recipe') ||
        lowerTitle.contains('12,000 recipes') ||
        lowerTitle.contains('12000 recipes');
  }

  // Check if file is a text file
  bool get isText => ['txt', 'doc', 'docx'].contains(fileExtension);

  // Get appropriate icon for file type
  String get fileIcon {
    if (isPdf) return '📄';
    if (isVideo) return '🎥';
    if (isText) return '📝';
    return '📁';
  }

  // Format file size for display
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  @override
  String toString() {
    return 'ContentItem(id: $id, title: $title, category: $category, fileType: $fileType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContentItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
