import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:firebase_database/firebase_database.dart';
import '../utils/logger.dart';

export 'firebase_storage_service.dart';
export 'realtime_database_service.dart';
export 'download_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(scopes: ['email']);
  final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Save user data to Realtime Database
  Future<void> _saveUserToDatabase(User user, {String? signUpMethod}) async {
    try {
      final userData = {
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName ?? '',
        'photoURL': user.photoURL ?? '',
        'role': 'user', // Default role is user
        'signUpMethod': signUpMethod ?? 'email',
        'createdAt': DateTime.now().toIso8601String(),
        'lastLoginAt': DateTime.now().toIso8601String(),
        'isActive': true,
      };

      // Save to users table
      await _database.child('users').child(user.uid).set(userData);

      Logger.info('User saved to database: ${user.email}');
    } catch (e) {
      Logger.error('Failed to save user to database', e);
      // Don't throw error as this shouldn't prevent authentication
    }
  }

  // Update last login time
  Future<void> _updateLastLogin(User user) async {
    try {
      await _database.child('users').child(user.uid).update({
        'lastLoginAt': DateTime.now().toIso8601String(),
      });
      Logger.info('Updated last login for user: ${user.email}');
    } catch (e) {
      Logger.error('Failed to update last login', e);
      // Don't throw error as this shouldn't prevent authentication
    }
  }

  // Check if user exists in Realtime Database
  Future<bool> _checkUserExistsInDatabase(String email) async {
    try {
      final snapshot = await _database.child('users').get();
      if (snapshot.exists) {
        final users = Map<String, dynamic>.from(snapshot.value as Map);
        // Check if any user has this email
        for (final userData in users.values) {
          if (userData['email'] == email) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      Logger.error('Error checking user in database', e);
      return false;
    }
  }

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      // First check if user exists in our database
      final userExists = await _checkUserExistsInDatabase(email.trim());
      if (!userExists) {
        throw FirebaseAuthException(
          code: 'user-not-found-in-database',
          message: 'Account not found in our system. Please sign up first.',
        );
      }

      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      // Update last login time
      if (userCredential.user != null) {
        await _updateLastLogin(userCredential.user!);
      }

      return userCredential;
    } catch (e) {
      rethrow; // Re-throw to handle specific errors in UI
    }
  }

  // Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      // First check if user already exists in our database
      final userExists = await _checkUserExistsInDatabase(email.trim());
      if (userExists) {
        throw FirebaseAuthException(
          code: 'user-already-exists-in-database',
          message:
              'Account already exists with this email. Please sign in instead.',
        );
      }

      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      // Save user to database
      if (userCredential.user != null) {
        await _saveUserToDatabase(userCredential.user!, signUpMethod: 'email');
      }

      return userCredential;
    } catch (e) {
      rethrow; // Re-throw to handle specific errors in UI
    }
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Always sign out first to show account picker
      await _googleSignIn.signOut();

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        return null;
      }

      // Check if user exists in our database first
      final userExists = await _checkUserExistsInDatabase(googleUser.email);
      if (!userExists) {
        // Sign out Google to prevent auto-login
        await _googleSignIn.signOut();
        throw FirebaseAuthException(
          code: 'user-not-found-in-database',
          message: 'Account not found in our system. Please sign up first.',
        );
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Check if we have the required tokens
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens');
      }

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in with credential
      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );

      // Update last login time
      if (userCredential.user != null) {
        await _updateLastLogin(userCredential.user!);
      }

      // Sign in successful - return the credential
      return userCredential;
    } catch (e) {
      Logger.error('Google Sign-In Error', e);
      rethrow; // Re-throw errors for proper handling
    }
  }

  // Sign up with Google (creates account and signs out for login flow)
  Future<UserCredential?> signUpWithGoogle() async {
    try {
      // Always sign out first to show account picker
      await _googleSignIn.signOut();

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-up
        return null;
      }

      // First check if user already exists in our database
      final userExists = await _checkUserExistsInDatabase(googleUser.email);
      if (userExists) {
        // Sign out Google to prevent auto-login
        await _googleSignIn.signOut();
        throw FirebaseAuthException(
          code: 'user-already-exists-in-database',
          message:
              'Account already exists with this email. Please sign in instead.',
        );
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Check if we have the required tokens
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens');
      }

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Create account by signing in
      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );

      // Save user to database
      if (userCredential.user != null) {
        await _saveUserToDatabase(userCredential.user!, signUpMethod: 'google');
      }

      // Account created successfully - now sign out the user so they need to sign in
      await _googleSignIn.signOut();
      await _auth.signOut();

      return userCredential;
    } catch (e) {
      Logger.error('Google Sign-Up Error', e);
      rethrow; // Re-throw errors for proper handling
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
    } catch (e) {
      rethrow; // Re-throw to handle specific errors in UI
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // Sign out from Google first
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }

      // Then sign out from Firebase
      await _auth.signOut();

      // Double check - force sign out if still signed in
      if (_auth.currentUser != null) {
        await _auth.signOut();
      }
    } catch (e) {
      // Force sign out even if there's an error
      try {
        await _auth.signOut();
      } catch (e2) {
        // Last resort - clear the auth state
      }
    }
  }

  // Delete account
  Future<bool> deleteAccount() async {
    try {
      User? user = _auth.currentUser;
      if (user != null) {
        await user.delete();
        await _googleSignIn.signOut();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
