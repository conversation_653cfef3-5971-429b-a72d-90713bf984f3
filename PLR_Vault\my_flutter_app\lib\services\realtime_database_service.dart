import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/models.dart';
import '../utils/logger.dart';

class RealtimeDatabaseService {
  static final RealtimeDatabaseService _instance =
      RealtimeDatabaseService._internal();
  factory RealtimeDatabaseService() => _instance;
  RealtimeDatabaseService._internal();

  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Database references
  DatabaseReference get _contentRef => _database.ref('content');
  DatabaseReference get _usersRef => _database.ref('users');

  // Category mapping - matches admin webapp structure
  static const Map<String, String> _categoryMapping = {
    // Storage folder name -> Full category name
    'Business': 'Business & Entrepreneurship',
    'Health': 'Health & Wellness',
    'Technology': 'Technology & Innovation',
    'Recipes': 'Recipes & Nutrition',
    'MRR': 'MRR Video Courses',
    'Finance': 'Finance & Investment',
    'SelfImprovement': 'Self-Improvement & Motivation',
    'Marketing': 'Marketing & Branding',
    'Design': 'Design & Templates',
    'Spirituality': 'Spirituality & Mindfulness',
    'Career': 'Career & Freelancing',
    'AI': 'AI & Automation',
    'Education': 'Education & eLearning',
    'Legal': 'Legal & Business Docs',
    'eCommerce': 'eCommerce & Dropshipping',
    'Parenting': 'Parenting & Family',
    'Fashion': 'Fashion & Beauty',
    'Travel': 'Travel & Lifestyle',
    'Kids': 'Kids & Learning',
    'Entertainment': 'Entertainment & Fun',
  };

  // Reverse mapping - Full category name -> Storage folder name
  static final Map<String, String> _reverseCategoryMapping = {
    for (var entry in _categoryMapping.entries) entry.value: entry.key,
  };

  /// Add content item to database
  Future<void> addContentItem(ContentItem item) async {
    try {
      // Get the storage folder name for this category
      final String storageCategory =
          _reverseCategoryMapping[item.category] ?? item.category;
      await _contentRef.child(storageCategory).child(item.id).set(item.toMap());
    } catch (e) {
      throw Exception('Failed to add content item: $e');
    }
  }

  /// Add multiple content items to database
  Future<void> addMultipleContentItems(List<ContentItem> items) async {
    try {
      final Map<String, dynamic> updates = {};

      for (final item in items) {
        // Get the storage folder name for this category
        final String storageCategory =
            _reverseCategoryMapping[item.category] ?? item.category;
        updates['content/$storageCategory/${item.id}'] = item.toMap();
      }

      await _database.ref().update(updates);
    } catch (e) {
      throw Exception('Failed to add multiple content items: $e');
    }
  }

  /// Get all content items in a category
  Future<List<ContentItem>> getContentByCategory(String category) async {
    try {
      Logger.info('Loading content for category: $category');

      // Get the storage folder name for this category
      final String storageCategory =
          _reverseCategoryMapping[category] ?? category;
      Logger.info('Storage category for $category: $storageCategory');

      // Get content from the storage category folder
      final DatabaseEvent event = await _contentRef
          .child(storageCategory)
          .once();
      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      Logger.info('Data found for $storageCategory: ${data != null}');
      if (data == null) {
        Logger.info('No content found for category: $category');
        return [];
      }

      Logger.info('Number of items in $storageCategory: ${data.length}');

      // Convert all items in this category
      final List<ContentItem> items = data.entries.map((entry) {
        return ContentItem.fromMap(entry.key, entry.value);
      }).toList();

      Logger.info('Final item count for $category: ${items.length}');
      return items;
    } catch (e) {
      Logger.error('Failed to get content by category: $category', e);
      throw Exception('Failed to get content by category: $e');
    }
  }

  /// Get all categories
  Future<List<String>> getCategories() async {
    try {
      // Return fixed categories instead of dynamic ones
      return [
        'Business & Entrepreneurship',
        'Health & Wellness',
        'Technology & Innovation',
        'Recipes & Nutrition',
        'MRR Video Courses',
        'Finance & Investment',
        'Self-Improvement & Motivation',
        'Marketing & Branding',
        'Design & Templates',
        'Spirituality & Mindfulness',
        'Career & Freelancing',
        'AI & Automation',
        'Education & eLearning',
        'Legal & Business Docs',
        'eCommerce & Dropshipping',
        'Parenting & Family',
        'Fashion & Beauty',
        'Travel & Lifestyle',
        'Kids & Learning',
        'Entertainment & Fun',
      ];
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  /// Get only categories that have content
  Future<List<String>> getCategoriesWithContent() async {
    try {
      Logger.info('Getting categories with content');
      final allCategories = await getCategories();
      final categoriesWithContent = <String>[];

      for (final category in allCategories) {
        final content = await getContentByCategory(category);
        if (content.isNotEmpty) {
          categoriesWithContent.add(category);
        }
      }

      Logger.info('Categories with content: $categoriesWithContent');
      return categoriesWithContent;
    } catch (e) {
      Logger.error('Failed to get categories with content', e);
      throw Exception('Failed to get categories with content: $e');
    }
  }

  /// Get original database categories (for internal use)
  Future<List<String>> _getDatabaseCategories() async {
    try {
      Logger.info('Fetching all database categories from content root');
      final DatabaseEvent event = await _contentRef.once();
      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) {
        Logger.warning('No data found in content root');
        return [];
      }

      // Get storage folder names and convert to full category names
      final List<String> storageFolders = data.keys.cast<String>().toList();
      final List<String> fullCategories = storageFolders
          .map((folder) => _categoryMapping[folder] ?? folder)
          .toList();

      Logger.info('Found storage folders: $storageFolders');
      Logger.info('Mapped to full categories: $fullCategories');
      return fullCategories;
    } catch (e) {
      Logger.error('Failed to get database categories', e);
      throw Exception('Failed to get database categories: $e');
    }
  }

  /// Get a specific content item
  Future<ContentItem?> getContentItem(String category, String itemId) async {
    try {
      // Get the storage folder name for this category
      final String storageCategory =
          _reverseCategoryMapping[category] ?? category;

      final DatabaseEvent event = await _contentRef
          .child(storageCategory)
          .child(itemId)
          .once();
      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) return null;

      return ContentItem.fromMap(itemId, data);
    } catch (e) {
      throw Exception('Failed to get content item: $e');
    }
  }

  /// Add download record for user
  Future<void> addUserDownload(UserDownload download) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');

      await _usersRef
          .child(userId)
          .child('downloads')
          .child(download.contentId)
          .set(download.toMap());
    } catch (e) {
      throw Exception('Failed to add user download: $e');
    }
  }

  /// Get all downloads for current user
  Future<List<UserDownload>> getUserDownloads() async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');

      final DatabaseEvent event = await _usersRef
          .child(userId)
          .child('downloads')
          .once();

      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) return [];

      return data.entries.map((entry) {
        return UserDownload.fromMap(entry.key, entry.value);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get user downloads: $e');
    }
  }

  /// Check if user has downloaded a specific content item
  Future<bool> hasUserDownloaded(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) return false;

      final DatabaseEvent event = await _usersRef
          .child(userId)
          .child('downloads')
          .child(contentId)
          .once();

      return event.snapshot.exists;
    } catch (e) {
      return false;
    }
  }

  /// Remove download record for user
  Future<void> removeUserDownload(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');

      await _usersRef
          .child(userId)
          .child('downloads')
          .child(contentId)
          .remove();
    } catch (e) {
      throw Exception('Failed to remove user download: $e');
    }
  }

  /// Update download status
  Future<void> updateDownloadStatus(String contentId, bool isDownloaded) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');

      await _usersRef
          .child(userId)
          .child('downloads')
          .child(contentId)
          .child('isDownloaded')
          .set(isDownloaded);
    } catch (e) {
      throw Exception('Failed to update download status: $e');
    }
  }

  /// Stream of user downloads (real-time updates)
  Stream<List<UserDownload>> getUserDownloadsStream() {
    final String? userId = _auth.currentUser?.uid;
    if (userId == null) return Stream.value([]);

    return _usersRef.child(userId).child('downloads').onValue.map((event) {
      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) return <UserDownload>[];

      return data.entries.map((entry) {
        return UserDownload.fromMap(entry.key, entry.value);
      }).toList();
    });
  }

  /// Stream of content by category (real-time updates)
  Stream<List<ContentItem>> getContentByCategoryStream(String category) {
    return _contentRef.child(category).onValue.map((event) {
      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) return <ContentItem>[];

      return data.entries.map((entry) {
        return ContentItem.fromMap(entry.key, entry.value);
      }).toList();
    });
  }

  // ============ CONTENT UNLOCKING METHODS ============

  /// Mark content as unlocked for current user
  Future<void> unlockContent(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');

      Logger.debug('Unlocking content $contentId for user $userId');

      await _usersRef
          .child(userId)
          .child('unlockedContent')
          .child(contentId)
          .set(true);

      Logger.debug('Content $contentId unlocked successfully in database');
    } catch (e) {
      Logger.error('Failed to unlock content $contentId', e);
      throw Exception('Failed to unlock content: $e');
    }
  }

  /// Check if content is unlocked for current user
  Future<bool> isContentUnlocked(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) {
        Logger.debug('No authenticated user for unlock check');
        return false;
      }

      final DatabaseEvent event = await _usersRef
          .child(userId)
          .child('unlockedContent')
          .child(contentId)
          .once();

      final isUnlocked = event.snapshot.exists && event.snapshot.value == true;
      Logger.debug(
        'Content $contentId unlock status: $isUnlocked (exists: ${event.snapshot.exists}, value: ${event.snapshot.value})',
      );

      return isUnlocked;
    } catch (e) {
      Logger.error('Error checking unlock status for $contentId', e);
      return false;
    }
  }

  /// Get all unlocked content IDs for current user
  Future<List<String>> getUnlockedContentIds() async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) return [];

      final DatabaseEvent event = await _usersRef
          .child(userId)
          .child('unlockedContent')
          .once();

      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) return [];

      return data.entries
          .where((entry) => entry.value == true)
          .map((entry) => entry.key.toString())
          .toList();
    } catch (e) {
      throw Exception('Failed to get unlocked content: $e');
    }
  }

  /// Get unlocked content items with their metadata
  Future<List<ContentItem>> getUnlockedContentItems() async {
    try {
      final unlockedIds = await getUnlockedContentIds();
      if (unlockedIds.isEmpty) return [];

      final List<ContentItem> unlockedItems = [];

      // Get all database categories first
      final categories = await _getDatabaseCategories();

      for (final category in categories) {
        final DatabaseEvent event = await _contentRef.child(category).once();
        final Map<dynamic, dynamic>? data =
            event.snapshot.value as Map<dynamic, dynamic>?;

        if (data != null) {
          final List<ContentItem> categoryItems = data.entries.map((entry) {
            return ContentItem.fromMap(entry.key, entry.value);
          }).toList();

          for (final item in categoryItems) {
            if (unlockedIds.contains(item.id)) {
              unlockedItems.add(item);
            }
          }
        }
      }

      return unlockedItems;
    } catch (e) {
      throw Exception('Failed to get unlocked content items: $e');
    }
  }

  /// Stream of unlocked content (real-time updates)
  Stream<List<String>> getUnlockedContentStream() {
    final String? userId = _auth.currentUser?.uid;
    if (userId == null) return Stream.value([]);

    return _usersRef.child(userId).child('unlockedContent').onValue.map((
      event,
    ) {
      final Map<dynamic, dynamic>? data =
          event.snapshot.value as Map<dynamic, dynamic>?;

      if (data == null) return <String>[];

      return data.entries
          .where((entry) => entry.value == true)
          .map((entry) => entry.key.toString())
          .toList();
    });
  }

  /// Remove unlocked content (for testing purposes)
  Future<void> removeUnlockedContent(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');

      await _usersRef
          .child(userId)
          .child('unlockedContent')
          .child(contentId)
          .remove();
    } catch (e) {
      throw Exception('Failed to remove unlocked content: $e');
    }
  }
}
